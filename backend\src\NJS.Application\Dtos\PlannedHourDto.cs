using System.Text.Json.Serialization;
using System.Globalization;

namespace NJS.Application.Dtos
{
    public class PlannedHourDto
    {
        public int Year { get; set; }
        public int MonthNo { get; set; } // Month number (1-12) - changed from string to int

        private DateTime? _date;

        [JsonPropertyName("date")]
        public string? DateString
        {
            get => _date?.ToString("yyyy-MM-dd"); // Use standard ISO format for output
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    _date = null;
                    return;
                }

                // Try to parse various date formats that frontend might send
                var formats = new[] {
                    "d-M-yyyy",     // 1-7-2025
                    "dd-MM-yyyy",   // 01-07-2025
                    "M-d-yyyy",     // 7-1-2025
                    "MM-dd-yyyy",   // 07-01-2025
                    "yyyy-MM-dd",   // 2025-07-01
                    "d/M/yyyy",     // 1/7/2025
                    "dd/MM/yyyy",   // 01/07/2025
                    "M/d/yyyy",     // 7/1/2025
                    "MM/dd/yyyy"    // 07/01/2025
                };

                foreach (var format in formats)
                {
                    if (DateTime.TryParseExact(value, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
                    {
                        _date = parsedDate;
                        return;
                    }
                }

                // Try general parsing as fallback
                if (DateTime.TryParse(value, out var generalDate))
                {
                    _date = generalDate;
                    return;
                }

                // If all parsing fails, set to null instead of throwing exception
                // This prevents JSON deserialization from crashing the application
                _date = null;
            }
        }

        [JsonIgnore]
        public DateTime? Date
        {
            get => _date;
            set => _date = value;
        }

        public int? WeekNo { get; set; } // Week number (1-53) - optional for weekly planning
        public double PlannedHours { get; set; }
        // public double? ActualHours { get; set; } // Future enhancement
    }
}
